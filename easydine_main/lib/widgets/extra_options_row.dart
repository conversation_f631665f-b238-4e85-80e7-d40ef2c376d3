import 'package:easydine_main/dialogs/addNotesDialog.dart';
import 'package:easydine_main/dialogs/miscellaneousItemsDialog.dart';
import 'package:easydine_main/dialogs/modifyBillDialog.dart';
import 'package:easydine_main/dialogs/warningItemsDialog.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

Widget extraOptionsRow(BuildContext context) {
  return Container(
    height: 4.h,
    padding: const EdgeInsets.symmetric(horizontal: 8),
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.05),
      borderRadius: BorderRadius.circular(12),
      border: Border.all(
        color: Colors.white.withOpacity(0.1),
        width: 1,
      ),
    ),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildActionButton(
          onTap: () => showMiscellaneousItemDialog(context),
          icon: Icons.add_shopping_cart_rounded,
          label: "Misc",
          color: const Color(0xFF2CBF5A),
        ),
        _buildDivider(),
        _buildActionButton(
          onTap: () => warningItemsNotesDialog(context),
          icon: Icons.warning_rounded,
          label: "Alert",
          color: Colors.redAccent,
        ),
        _buildDivider(),
        _buildActionButton(
          onTap: () => showAddNotesDialog(context),
          icon: Icons.note_add_rounded,
          label: "Notes",
          color: Colors.orangeAccent,
        ),
        _buildDivider(),
        _buildActionButton(
          onTap: () => modifyBillDialog(context),
          icon: Icons.tips_and_updates_rounded,
          label: "Bill",
          color: Colors.blueAccent,
        ),
      ],
    ),
  );
}

Widget _buildActionButton({
  required VoidCallback onTap,
  required IconData icon,
  required String label,
  required Color color,
}) {
  return Material(
    color: Colors.transparent,
    child: InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 1.5.h,
              color: color,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontSize: 3.5.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    ),
  );
}

Widget _buildDivider() {
  return Container(
    height: 2.h,
    width: 1,
    margin: const EdgeInsets.symmetric(horizontal: 4),
    color: Colors.white.withOpacity(0.1),
  );
}
