import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../blocs/cart/cart_bloc.dart';
import '../blocs/cart/cart_state.dart';
import '../models/cartItem.dart';
import '../models/cart_models.dart';
import 'cart_item_tile.dart';

class CartItemsList extends StatelessWidget {
  const CartItemsList({super.key});

  // Convert ServerCartItem to CartItem for display
  CartItem _convertToCartItem(ServerCartItem serverItem) {
    debugPrint(
        '🛒 CartItemsList: Converting ServerCartItem ${serverItem.id} with quantity ${serverItem.quantity}');
    final cartItem = CartItem(
      id: serverItem.id, // Use cartItemId for unique identification
      name: serverItem.name, // Use actual name from API
      price: serverItem.price, // Use actual price from API
      quantity: serverItem.quantity,
      customization: {
        'notes': serverItem.notes,
        'type': serverItem.type,
        'allergyIds': serverItem.allergyIds,
        'dishAddons': serverItem.dishAddons.map((e) => e.toJson()).toList(),
        'dishExtras': serverItem.dishExtras.map((e) => e.toJson()).toList(),
        'dishSides': serverItem.dishSides.map((e) => e.toJson()).toList(),
        'dishBeverages':
            serverItem.dishBeverages.map((e) => e.toJson()).toList(),
        'dishDesserts': serverItem.dishDesserts.map((e) => e.toJson()).toList(),
      },
    );
    debugPrint(
        '🛒 CartItemsList: Created CartItem ${cartItem.id} with quantity ${cartItem.quantity}');
    return cartItem;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CartBloc, CartState>(
      builder: (context, cartState) {
        debugPrint(
            '🛒 CartItemsList: BlocBuilder rebuilding with state: ${cartState.runtimeType}');
        final cartItems = cartState.currentCart?.items ?? [];
        debugPrint('🛒 CartItemsList: Found ${cartItems.length} cart items');

        if (cartItems.isEmpty) {
          return Container(
            decoration: BoxDecoration(
              color: Colors.transparent,
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 8.sp,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Your cart is empty',
                    style: GoogleFonts.dmSans(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.zero,
          itemCount: cartItems.length,
          itemBuilder: (context, index) {
            return CartItemTile(item: _convertToCartItem(cartItems[index]));
          },
        );
      },
    );
  }
}
