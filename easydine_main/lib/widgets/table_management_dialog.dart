import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../blocs/table/table_bloc.dart';
import '../blocs/table/table_event.dart';

class TableManagementDialog extends StatefulWidget {
  final Map<String, dynamic> table;

  const TableManagementDialog({
    Key? key,
    required this.table,
  }) : super(key: key);

  @override
  State<TableManagementDialog> createState() => _TableManagementDialogState();
}

class _TableManagementDialogState extends State<TableManagementDialog> {
  final _seatsController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _seatsController.text = widget.table['bookedSeats']?.toString() ?? '0';
  }

  @override
  void dispose() {
    _seatsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final table = widget.table;
    final primaryColor = Color(0xFF2CBF5A);
    final backgroundColor = Color(0xFF1A1A1A);
    final surfaceColor = Color(0xFF2D2D2D);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 400,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: surfaceColor,
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.table_restaurant,
                    color: primaryColor,
                    size: 28,
                  ),
                  SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Manage Table ${table['tableNumber']}',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        'Status: ${table['status']} • ${table['seats']} seats',
                        style: GoogleFonts.poppins(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status Management
                  Text(
                    'Table Status',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12),
                  Row(
                    children: [
                      _buildStatusButton('Available', Colors.green),
                      SizedBox(width: 8),
                      _buildStatusButton('Occupied', Colors.orange),
                      SizedBox(width: 8),
                      _buildStatusButton('Reserved', Colors.blue),
                    ],
                  ),

                  SizedBox(height: 20),

                  // Cleaning Status
                  Text(
                    'Cleaning Status',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12),
                  Row(
                    children: [
                      _buildCleaningButton('Clean', Colors.green),
                      SizedBox(width: 8),
                      _buildCleaningButton('Needs Cleaning', Colors.orange),
                      SizedBox(width: 8),
                      _buildCleaningButton('Dirty', Colors.red),
                    ],
                  ),

                  SizedBox(height: 20),

                  // Booked Seats
                  Text(
                    'Booked Seats',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _seatsController,
                          keyboardType: TextInputType.number,
                          style: GoogleFonts.poppins(color: Colors.white),
                          decoration: InputDecoration(
                            hintText: 'Number of seats',
                            hintStyle: GoogleFonts.poppins(color: Colors.white54),
                            filled: true,
                            fillColor: surfaceColor,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide.none,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      ElevatedButton(
                        onPressed: _updateBookedSeats,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: primaryColor,
                          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          'Update',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 20),

                  // Quick Actions
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _resetTable,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[700],
                            padding: EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Reset Table',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.pop(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: primaryColor,
                            padding: EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Close',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusButton(String status, Color color) {
    final isSelected = widget.table['status'].toString().toLowerCase() == status.toLowerCase();
    
    return Expanded(
      child: ElevatedButton(
        onPressed: () => _updateTableStatus(status),
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? color : color.withValues(alpha: 0.2),
          padding: EdgeInsets.symmetric(vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
            side: BorderSide(
              color: color,
              width: isSelected ? 2 : 1,
            ),
          ),
        ),
        child: Text(
          status,
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : color,
          ),
        ),
      ),
    );
  }

  Widget _buildCleaningButton(String status, Color color) {
    final isSelected = widget.table['cleaningStatus'].toString().toLowerCase() == status.toLowerCase();
    
    return Expanded(
      child: ElevatedButton(
        onPressed: () => _updateCleaningStatus(status),
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? color : color.withValues(alpha: 0.2),
          padding: EdgeInsets.symmetric(vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
            side: BorderSide(
              color: color,
              width: isSelected ? 2 : 1,
            ),
          ),
        ),
        child: Text(
          status,
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected ? Colors.white : color,
          ),
        ),
      ),
    );
  }

  void _updateTableStatus(String status) {
    final tableBloc = context.read<TableBloc>();
    tableBloc.add(UpdateTableStatus(
      tableId: widget.table['id'] as String,
      newStatus: status,
    ));
    Navigator.pop(context);
    _showSuccessMessage('Table status updated to $status');
  }

  void _updateCleaningStatus(String status) {
    final tableBloc = context.read<TableBloc>();
    tableBloc.add(UpdateTableCleaningStatus(
      tableId: widget.table['id'] as String,
      newStatus: status,
    ));
    Navigator.pop(context);
    _showSuccessMessage('Cleaning status updated to $status');
  }

  void _updateBookedSeats() {
    final seats = int.tryParse(_seatsController.text) ?? 0;
    final tableBloc = context.read<TableBloc>();
    tableBloc.add(UpdateTableBookedSeats(
      tableId: widget.table['id'] as String,
      bookedSeats: seats,
    ));
    Navigator.pop(context);
    _showSuccessMessage('Booked seats updated to $seats');
  }

  void _resetTable() {
    final tableBloc = context.read<TableBloc>();
    tableBloc.add(ResetTable(
      tableId: widget.table['id'] as String,
    ));
    Navigator.pop(context);
    _showSuccessMessage('Table reset successfully');
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.poppins(),
        ),
        backgroundColor: Color(0xFF2CBF5A),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}
